package com.eftaapay.payment.api.service;


import com.eftaapay.payment.dto.api.StaticQRDetailsDto;
import com.eftaapay.payment.dto.api.StaticQRRequestDto;
import com.eftaapay.payment.dto.api.StaticQRResponseDto;
import com.eftaapay.payment.entity.StaticQR;
import com.eftaapay.payment.entity.User;
import com.eftaapay.payment.repository.SecurityCredentialRepository;
import com.eftaapay.payment.repository.StaticQRRepository;
import com.eftaapay.payment.repository.TransactionRepository;
import com.eftaapay.payment.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class StaticQRService {

    private final Logger logger = LoggerFactory.getLogger(StaticQRService.class);

    @Autowired
    private StaticQRRepository staticQRRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SecurityCredentialRepository securityCredentialRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private PaymentService paymentService;

    public StaticQRResponseDto createStaticQR(StaticQRRequestDto staticQRRequestDto) {

        // Check if user already has a static QR code
        if (staticQRRepository.existsByUserId(staticQRRequestDto.getParentUserId())) {
            throw new IllegalStateException("User already has a static QR code");
        }

        StaticQR staticQR = new StaticQR();
        staticQR.setUser(userRepository.findById(staticQRRequestDto.getParentUserId()).get());
        staticQR.setDefaultAmount(staticQRRequestDto.getDefaultAmount());
        staticQR.setName(staticQRRequestDto.getName());

        StaticQR savedStaticQR = staticQRRepository.save(staticQR);

        return convertToDto(savedStaticQR);
    }

    public List<StaticQRResponseDto> getStaticQRs(UUID parentUserId) {

        List<StaticQR> staticQRs = staticQRRepository.findByUserId(parentUserId);

        return staticQRs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    public Optional<StaticQRResponseDto> getStaticQRById(UUID qrcodeId) {

        return staticQRRepository.findById(qrcodeId)
                .map(this::convertToDto);
    }

    public StaticQRResponseDto updateStaticQR(User user, UUID id, StaticQRRequestDto staticQRRequestDto) {

        StaticQR staticQR = staticQRRepository.findByUserIdAndId(user.getId(), id)
                .orElseThrow(() -> new IllegalArgumentException("Static QR not found or not owned by user"));

        if (staticQRRequestDto.getDefaultAmount() != null) {
            staticQR.setDefaultAmount(staticQRRequestDto.getDefaultAmount());
        }

        if (staticQRRequestDto.getName() != null) {
            staticQR.setName(staticQRRequestDto.getName());
        }

        StaticQR updatedStaticQR = staticQRRepository.save(staticQR);

        return convertToDto(updatedStaticQR);
    }

    public void deleteStaticQR(User user, UUID id) {

        StaticQR staticQR = staticQRRepository.findByUserIdAndId(user.getId(), id)
                .orElseThrow(() -> new IllegalArgumentException("Static QR not found or not owned by user"));

        staticQRRepository.delete(staticQR);
    }


    private StaticQRResponseDto convertToDto(StaticQR staticQR) {
        return new StaticQRResponseDto(
                staticQR.getId(),
                staticQR.getUser().getId(),
                staticQR.getDefaultAmount(),
                staticQR.getName(),
                staticQR.getCreatedAt()
        );
    }

    @Transactional(readOnly = true)
    public Optional<StaticQRDetailsDto> getStaticQRDetails(UUID staticQRId) {
        try {
            Optional<StaticQR> staticQROptional = staticQRRepository.findByIdWithUser(staticQRId);

            if (staticQROptional.isEmpty()) {
                logger.warn("Static QR code not found with ID: {}", staticQRId);
                return Optional.empty();
            }

            StaticQR staticQR = staticQROptional.get();
            User user = staticQR.getUser();

            if (user == null) {
                logger.warn("User not found for static QR code with ID: {}", staticQRId);
                return Optional.empty();
            }

            StaticQRDetailsDto detailsDto = new StaticQRDetailsDto(
                    user.getFirstName(),
                    user.getLastName(),
                    user.getCompany(),
                    user.getCurrency(),
                    staticQR.getName(),
                    staticQR.getDefaultAmount()
            );

            return Optional.of(detailsDto);
        } catch (Exception e) {
            logger.error("Error retrieving static QR details for ID: {}", staticQRId, e);
            return Optional.empty();
        }
    }
}

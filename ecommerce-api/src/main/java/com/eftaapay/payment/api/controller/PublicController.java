package com.eftaapay.payment.api.controller;

import com.eftaapay.payment.api.enums.TransactionAction;
import com.eftaapay.payment.api.service.QRCodeAuthService;
import com.eftaapay.payment.api.service.StaticQRService;
import com.eftaapay.payment.dto.api.CheckoutRequestDto;
import com.eftaapay.payment.dto.api.StaticQRDetailsDto;
import com.eftaapay.payment.dto.api.TransactionDTO;
import com.eftaapay.payment.dto.common.ApiResponseDto;
import com.eftaapay.payment.dto.api.CheckoutResponseDto;
import com.eftaapay.payment.entity.QRCode;
import com.eftaapay.payment.entity.Transaction;
import com.eftaapay.payment.api.service.PaymentService;
import com.eftaapay.payment.api.service.TransactionService;
import com.onlinepayments.domain.GetHostedCheckoutResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import javax.validation.Valid;
import java.net.URI;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RestController
@Tag(name = "Checkout", description = "Endpoints related to checkout and transaction management")
@RequestMapping("/public")
public class PublicController {

    Logger logger = LoggerFactory.getLogger(PublicController.class);

    @Resource
    private TransactionService transactionService;

    @Resource
    private PaymentService paymentService;

    @Resource
    private QRCodeAuthService qrCodeAuthService;

    @Resource
    private StaticQRService staticQRService;

    @Operation(summary = "API Health Check", description = "Check the health status of the Online Payment API.")
    @ApiResponse(responseCode = "200", description = "API is operational")
    @GetMapping
    public String healthCheck() {
        return "I am Up and Running - Online API";
    }

    @Operation(summary = "Initiate Checkout",
            description = "Initiate the checkout process by providing a transaction ID. If a redirect URL is available, the user will be redirected; otherwise, the transaction status will be returned.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Checkout initiated successfully"),
            @ApiResponse(responseCode = "302", description = "Redirected to the checkout URL"),
            @ApiResponse(responseCode = "404", description = "Transaction not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/checkout")
    public ResponseEntity<?> checkout(@RequestParam("transactionId") String transactionIdString) {
        UUID transactionId = UUID.fromString(transactionIdString);

        Optional<Transaction> optionalTransaction = transactionService.findById(transactionId);

        if (optionalTransaction.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponseDto<>(false, "Transaction not found", null));
        }

        Transaction transaction = optionalTransaction.get();
        String externalId = transaction.getExternalId();
        String redirectUrl = transaction.getRedirectUrl();
        String transactionStatus = transaction.getStatus();

        if (redirectUrl != null && !redirectUrl.isEmpty()) {
            HttpHeaders headers = new HttpHeaders();
            headers.setLocation(URI.create(redirectUrl));
            return new ResponseEntity<>(headers, HttpStatus.FOUND);
        }

        CheckoutResponseDto checkoutResponseDto = new CheckoutResponseDto(redirectUrl, transactionStatus, externalId);
        ApiResponseDto<CheckoutResponseDto> response = new ApiResponseDto<>(
                true,
                "Transaction found, but no redirect URL available",
                checkoutResponseDto
        );

        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get Checkout Status",
            description = "Fetch the status of a hosted checkout using the hosted checkout ID. This endpoint also updates the transaction status based on the response.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Checkout status retrieved and transaction updated successfully"),
            @ApiResponse(responseCode = "404", description = "Hosted checkout ID not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/checkout-status")
    public RedirectView checkoutStatus(@RequestParam("hostedCheckoutId") String hostedCheckoutId) {
        boolean updated = false;
        logger.debug("Received hostedCheckoutId: {}", hostedCheckoutId);

        GetHostedCheckoutResponse response = paymentService.getHostedCheckoutStatus(hostedCheckoutId);

        String status = response.getStatus();

        String baseReturnUrl = transactionService.getReturnUrlByExternalId(hostedCheckoutId);
        if (baseReturnUrl == null) {
            logger.error("No return URL found for hostedCheckoutId: {}", hostedCheckoutId);
            return new RedirectView("https://hazelsone.com/return");
        }

        String returnUrl;
        if (status.equals("PAYMENT_CREATED")) {
            updated = transactionService.updateTransactionStatus(hostedCheckoutId, TransactionAction.AUTHORISED.toString());

            Optional<Transaction> transaction = transactionService.findByExternalId(hostedCheckoutId);
            if (transaction.isPresent()) {
                String transactionId = transaction.get().getId().toString();
                transactionService.updateQRCodeStatusByTransactionId(transactionId, QRCode.QRCodeStatus.REDEEMED.name());
                logger.info("Updated QRCode status to REDEEMED for transaction ID: {}", transactionId);
            } else {
                logger.warn("Transaction not found for hostedCheckoutId: {}", hostedCheckoutId);
            }

            returnUrl = baseReturnUrl + "?status=success";
        } else if (status.equals("CANCELLED_BY_CONSUMER")) {
            updated = transactionService.updateTransactionStatus(hostedCheckoutId, TransactionAction.CANCELLED.toString());
            returnUrl = baseReturnUrl + "?status=failed";
        } else {
            updated = transactionService.updateTransactionStatus(hostedCheckoutId, status);
            returnUrl = baseReturnUrl + "?status=unknown";
        }

        if (!updated) {
            logger.error("Failed to update the transaction status for hostedCheckoutId: {}", hostedCheckoutId);
        }

        return new RedirectView(returnUrl);
    }

    @Operation(summary = "Get Transaction by ID",
            description = "Fetch transaction details using the transaction ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Transaction retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Transaction not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/transaction")
    public ResponseEntity<ApiResponseDto<TransactionDTO>> getTransactionById(@RequestParam("transactionId") String transactionId) {
        try {
            UUID tId = UUID.fromString(transactionId);

            TransactionDTO transaction = transactionService.getTransactionById(tId);

            if (transaction == null) {
                logger.warn("Transaction not found for ID: {}", transactionId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponseDto<>(false, "Transaction not found", null));
            }

            logger.debug("Transaction retrieved successfully for ID: {}", transactionId);
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponseDto<>(true, "Transaction found", transaction));
        } catch (IllegalArgumentException e) {
            logger.error("Invalid UUID format for transactionId: {}", transactionId);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error retrieving transaction for ID: {} - {}", transactionId, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }
    @Operation(summary = "Create Static QR Payment", description = "Create a new payment transaction using the user's static QR code and checkout response. Returns the transaction ID if successful.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Static QR Payment created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or static QR not created"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/create-static-qr-payment")
    public ResponseEntity<ApiResponseDto<UUID>> createStaticQRPayment(
            @Valid @RequestBody CheckoutRequestDto checkoutRequestDto,
            @RequestParam(value = "staticQRId") UUID staticQRId) {

        try {
            ApiResponseDto<UUID> response = paymentService.createPaymentForStaticQR(
                    checkoutRequestDto, staticQRId, true);

            if (!response.isStatus()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error creating static QR payment: {}", e.getMessage());
            return ResponseEntity.internalServerError()
                    .body(new ApiResponseDto<>(false, "Failed to create static QR payment", null));
        }
    }

    @Operation(summary = "Get Static QR Details",
            description = "Retrieve static QR code details along with user information (first name, last name, company name, and currency).")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Static QR details retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "Static QR not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/static-qr-details")
    public ResponseEntity<ApiResponseDto<StaticQRDetailsDto>> getStaticQRDetails(
            @RequestParam("staticQRId") UUID staticQRId) {

        try {
            Optional<StaticQRDetailsDto> staticQRDetails = staticQRService.getStaticQRDetails(staticQRId);

            if (staticQRDetails.isEmpty()) {
                logger.warn("Static QR details not found for ID: {}", staticQRId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponseDto<>(false, "Static QR details not found", null));
            }

            logger.debug("Static QR details retrieved successfully for ID: {}", staticQRId);
            return ResponseEntity.ok(new ApiResponseDto<>(true, "Static QR details found", staticQRDetails.get()));

        } catch (Exception e) {
            logger.error("Error retrieving static QR details for ID: {}", staticQRId, e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponseDto<>(false, "Failed to retrieve static QR details", null));
        }
    }
}

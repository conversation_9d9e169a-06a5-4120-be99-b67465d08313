package com.eftaapay.payment.onboarding.controller;

import com.eftaapay.payment.dto.common.ApiResponseDto;
import com.eftaapay.payment.dto.onboarding.*;
import com.eftaapay.payment.entity.User;
import com.eftaapay.payment.exception.EmailNotFoundException;
import com.eftaapay.payment.onboarding.exception.UserNotFoundException;
import com.eftaapay.payment.onboarding.service.OnboardingService;
import com.eftaapay.payment.onboarding.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.ValidationException;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api")
@Tag(name = "Onboarding", description = "Operations related to user onboarding and management")
public class OnboardingController {

    Logger logger = LoggerFactory.getLogger(OnboardingController.class);

    @Resource
    private OnboardingService onboardingService;

    @Resource
    private SecurityUtil securityUtil;

    public OnboardingController() {
    }

    @PutMapping("/user/additional-data")
    public ResponseEntity<ApiResponseDto<UserResponseDto>> addAdditionalData(
            @AuthenticationPrincipal UserDetails userDetails,
            @Valid @RequestBody UserRequestDto additionalData) {
        try {
            String authenticatedEmail = userDetails.getUsername();
            String requestEmail = additionalData.getEmail();

            if (!authenticatedEmail.equals(requestEmail)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new ApiResponseDto<>(false, "Unauthorized: Email mismatch", null));
            }

            User currentUser = securityUtil.getCurrentUser(userDetails);

            additionalData.setEmail(authenticatedEmail);

            ApiResponseDto<UserResponseDto> responseDto = onboardingService.addAdditionalUserData(additionalData);
            return ResponseEntity.ok(responseDto);
        } catch (EmailNotFoundException | NoSuchElementException e) {
            logger.error("User not found: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponseDto<>(false, "User not found", null));
        } catch (Exception e) {
            logger.error("Error adding additional user data: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponseDto<>(false, e.getMessage(), null));
        }
    }

    @PatchMapping("/user/update")
    public ResponseEntity<ApiResponseDto<UserResponseDto>> updateUserFields(
            @AuthenticationPrincipal UserDetails userDetails,
            @RequestBody UserUpdateRequestDto updateData) {
        try {
            String authenticatedEmail = userDetails.getUsername();

            ApiResponseDto<UserResponseDto> responseDto = onboardingService.updateUserFields(authenticatedEmail, updateData);

            return ResponseEntity.ok(responseDto);

        } catch (UserNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponseDto<>(false, "User not found", null));
        } catch (ValidationException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponseDto<>(false, e.getMessage(), null));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponseDto<>(false, "An unexpected error occurred", null));
        }
    }

    @PostMapping("/user/enable")
    @Operation(summary = "Enable User", description = "Enables a user by their email, with optional terminal details if the user is a TERMINAL user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User enabled successfully"),
            @ApiResponse(responseCode = "400", description = "Bad request - invalid input or user state"),
            @ApiResponse(responseCode = "404", description = "User not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ApiResponseDto<Void>> enableUser(
            @Valid @RequestBody EnableUserRequestDto enableUserRequestDto) {
        try {
            if (enableUserRequestDto.getEmail() == null || enableUserRequestDto.getEmail().trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(new ApiResponseDto<>(false, "Email is required", null));
            }

            onboardingService.enableUser(enableUserRequestDto.getEmail(), enableUserRequestDto.getTerminalRequest());
            logger.info("User with email {} enabled successfully", enableUserRequestDto.getEmail());
            return ResponseEntity.ok(new ApiResponseDto<>(true, "User enabled successfully", null));
        } catch (NoSuchElementException e) {
            logger.error("User with email {} not found: {}", enableUserRequestDto.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(new ApiResponseDto<>(false, e.getMessage(), null));
        } catch (IllegalStateException | IllegalArgumentException | UnsupportedOperationException e) {
            logger.error("Validation error for user with email {}: {}", enableUserRequestDto.getEmail(), e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponseDto<>(false, e.getMessage(), null));
        } catch (Exception e) {
            logger.error("Error enabling user with email {}: {}", enableUserRequestDto.getEmail(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponseDto<>(false, "An unexpected error occurred: " + e.getMessage(), null));
        }
    }


    @GetMapping("/user/sub-merchant")
    @Operation(summary = "Get Sub merchant Accounts", description = "Retrieves sub merchant accounts of the currently authenticated parent user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Sub merchant accounts retrieved successfully"),
            @ApiResponse(responseCode = "500", description = "Error retrieving sub merchant accounts")
    })
    public ResponseEntity<ApiResponseDto<List<UserResponseDto>>> getChildAccounts(@AuthenticationPrincipal UserDetails userDetails) {
        try {
            User currentUser = securityUtil.getCurrentUser(userDetails);
            List<User> childAccounts = onboardingService.getChildAccountsByParentId(currentUser.getId());

            List<UserResponseDto> userChildAccounts = childAccounts.stream()
                    .map(UserResponseDto::new)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(new ApiResponseDto<>(true, "Sub merchant accounts retrieved successfully", userChildAccounts));
        } catch (Exception e) {
            logger.error("Error retrieving sub merchant accounts: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponseDto<>(false, "Error retrieving sub merchant accounts", null));
        }
    }

    @PostMapping("/create-issue")
    public ResponseEntity<ApiResponseDto<String>> createIssue(@AuthenticationPrincipal UserDetails userDetails, @RequestBody JiraIssueRequestDto issueRequest) {
        try {
            User currentUser = securityUtil.getCurrentUser(userDetails);
            String username = currentUser.getFirstName() + " " + currentUser.getLastName();

            String response = onboardingService.createIssue(issueRequest, username, currentUser.getEmail());

            return ResponseEntity.ok(new ApiResponseDto<>(true, "Support Ticket created successfully.", response));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ApiResponseDto<>(false, "Invalid request. Please check the input fields and try again.", null));
        } catch (Exception e) {
            logger.error("Error while creating Jira issue: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponseDto<>(false, "An unexpected error occurred while creating the support ticket. Please try again later.", null));
        }
    }
}

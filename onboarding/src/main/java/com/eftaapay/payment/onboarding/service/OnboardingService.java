package com.eftaapay.payment.onboarding.service;

import com.eftaapay.payment.dto.cardpresent.MerchantRegistrationRequestDto;
import com.eftaapay.payment.dto.cardpresent.RegisterPaymentTerminalDTO;
import com.eftaapay.payment.dto.common.ApiResponseDto;
import com.eftaapay.payment.dto.onboarding.*;
import com.eftaapay.payment.entity.*;
import com.eftaapay.payment.exception.EmailNotFoundException;
import com.eftaapay.payment.onboarding.enums.MerchantType;
import com.eftaapay.payment.onboarding.util.TerminalNameGenerator;
import com.eftaapay.payment.repository.*;
import com.eftaapay.payment.service.email.EmailService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.WordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import com.eftaapay.payment.exception.EmailAlreadyExistsException;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;

import static com.eftaapay.payment.onboarding.util.JiraIssueUtils.*;

@Service
public class OnboardingService {

    public static final String SUBJECT = "Merchant created";
    public static final String ONBOARDING_REQUEST_SUBJECT = "Onboarding Request";
    public static final String SUPPORT_TICKET_SUBJECT = "Support Ticket Created";
    public static final String SUCCESSFULLY_UPDATED = "User successfully updated";
    public static final String BCC = "<EMAIL>";
    public static final String ACCOUNT_ENABLED_NOTIFICATION_SUBJECT = "Merchant Account Enabled Notification";
    private final UserRepository userRepository;
    private final UserRoleRepository userRoleRepository;
    private final UserDetailsRepository userDetailsRepository;
    private final PasswordEncoder passwordEncoder;
    private final PaymentTerminalRepository paymentTerminalRepository;
    private final OnboardingRequestRepository onboardingRequestRepository;
    private UserDetails userDetails;
    private User savedUser;
    private final TerminalNameGenerator terminalNameGenerator;
    private final Environment environment;
    private static final Logger logger = LoggerFactory.getLogger(OnboardingService.class);

    @Value("${iba.api.username}")
    private String ibaAPIUsername;

    @Value("${iba.api.password}")
    private String ibaAPIPassword;

    @Value("${card-present.base.url}")
    private String cardPresentBaseUrl;

    @Value("${iba.base-url}")
    private String ibaBaseUrl;

    @Value("${jira.url}")
    private String jiraUrl;

    @Value("${jira.username}")
    private String jiraUsername;

    @Value("${jira.api.token}")
    private String jiraApiToken;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Autowired
    private EmailService emailService;

    private final ObjectMapper objectMapper;

    @Autowired
    public OnboardingService(UserRepository userRepository, UserRoleRepository userRoleRepository, UserDetailsRepository userDetailsRepository, PasswordEncoder passwordEncoder, PaymentTerminalRepository paymentTerminalRepository, OnboardingRequestRepository onboardingRequestRepository, TerminalNameGenerator terminalNameGenerator,ObjectMapper objectMapper, Environment environment) {
        this.userRepository = userRepository;
        this.userRoleRepository = userRoleRepository;
        this.userDetailsRepository = userDetailsRepository;
        this.passwordEncoder = passwordEncoder;
        this.paymentTerminalRepository = paymentTerminalRepository;
        this.terminalNameGenerator = terminalNameGenerator;
        userDetails = null;
        savedUser = null;
        this.onboardingRequestRepository = onboardingRequestRepository;
        this.objectMapper = objectMapper;
        this.environment = environment;
    }

    public ApiResponseDto<UUID> createBasicUser(LoginDto user) throws EmailAlreadyExistsException {
        if (userRepository.findByEmail(user.getEmail()).isPresent()) {
            throw new EmailAlreadyExistsException("Email already exists");
        }

        User newUser = new User();
        newUser.setEmail(user.getEmail());
        newUser.setPassword(passwordEncoder.encode(user.getPassword()));

        if (user.getParentId() != null) {
            User parentUser = userRepository.findById(user.getParentId())
                    .orElseThrow(() -> new NoSuchElementException("Parent User not found"));

            if (parentUser.getUpdatedAt() == null) {
                throw new UnsupportedOperationException("Please complete user registration of parent account to register terminal user");
            }

            if(parentUser.getMerchantType().equalsIgnoreCase(MerchantType.CARDPRESENT.name()) || parentUser.getMerchantType().equalsIgnoreCase(MerchantType.GROUP.name())){
                newUser.setMerchantType(MerchantType.TERMINAL.name());
            }else{
                newUser.setMerchantType(parentUser.getMerchantType());
            }

            newUser.setParentId(user.getParentId());
        }

        newUser = userRepository.save(newUser);

        assignUserRole(newUser, 1L);

        emailService.sendEmailWithTemplate(
                user.getEmail(),
                BCC,
                SUBJECT,
                EmailService.BASIC_SIGNUP_TEMPLATE
        );

        return new ApiResponseDto<>(true, "User created successfully", newUser.getId());
    }

    public ApiResponseDto<UserResponseDto> addAdditionalUserData(UserRequestDto user) throws Exception {
        userDetails = null;
        savedUser = null;

        // Validate merchant type
        String merchantType = user.getMerchantType();
        if (!MerchantType.ONLINE.name().equalsIgnoreCase(merchantType) &&
            !MerchantType.CARDPRESENT.name().equalsIgnoreCase(merchantType) &&
            !MerchantType.GROUP.name().equalsIgnoreCase(merchantType) &&
            !MerchantType.TERMINAL.name().equalsIgnoreCase(merchantType)) {
            return new ApiResponseDto<>(false, "Invalid merchant type: " + merchantType, null);
        }

        // Find existing user
        User existingUser = userRepository.findByEmail(user.getEmail())
                .orElseThrow(() -> new EmailNotFoundException("User with email " + user.getEmail() + " not found."));

        // Map user data
        User updatedUser = mapToUserEntity(user, existingUser);

        // Handle parent ID if provided
        if (user.getParentId() != null) {
            if(!existingUser.getId().equals(user.getParentId())){
                User parentUser = userRepository.findById(user.getParentId())
                        .orElseThrow(() -> new NoSuchElementException("Parent user not found with ID: " + user.getParentId()));
                existingUser.setParentId(user.getParentId());
            } else {
                throw new Exception("Parent ID cannot be the same as the user's own ID");
            }
        }

        // Check if user can be updated
        if (existingUser.getUpdatedAt() == null) {
            existingUser.setUpdatedAt(LocalDateTime.now());
        } else {
            throw new UnsupportedOperationException("User data can only be updated once.");
        }

        // Handle different merchant types
        if (MerchantType.CARDPRESENT.name().equalsIgnoreCase(merchantType)) {
            if (user.getUserDetails() == null) {
                throw new Exception("UserDetails are required for CARDPRESENT merchant type");
            }

            Optional<UserDetails> existingUserDetails = userDetailsRepository.findByUnp(user.getUserDetails().getUnp());
            if(existingUserDetails.isPresent()){
                throw new IllegalArgumentException("User with taxpayer id already exists");
            }

            user.getUserDetails().validateFields();

            savedUser = userRepository.save(updatedUser);
            userDetails = mapToUserDetailsEntity(user.getUserDetails(), savedUser);
            userDetailsRepository.save(userDetails);
        } else if (MerchantType.GROUP.name().equalsIgnoreCase(merchantType)) {
            if (user.getUserDetails() != null) {
                Optional<UserDetails> existingUserDetails = userDetailsRepository.findByUnp(user.getUserDetails().getUnp());
                if(existingUserDetails.isPresent()){
                    throw new IllegalArgumentException("User with taxpayer id already exists");
                }
            }

            savedUser = userRepository.save(existingUser);
            if (user.getUserDetails() != null) {
                userDetails = mapToUserDetailsEntity(user.getUserDetails(), savedUser);
                userDetailsRepository.save(userDetails);
            }
        } else if (MerchantType.ONLINE.name().equalsIgnoreCase(merchantType)) {
            savedUser = userRepository.save(existingUser);
        } else if (MerchantType.TERMINAL.name().equalsIgnoreCase(merchantType)) {
            // Handle terminal type users
            savedUser = userRepository.save(existingUser);
        }

        // Make sure savedUser is set
        if (savedUser == null) {
            savedUser = userRepository.save(existingUser);
        }

        emailService.sendEmailWithTemplate(user.getEmail(), BCC, SUBJECT, EmailService.MERCHANT_ONBOARDING_TEMPLATE, user.getFirstName(),user.getLastName());
        return new ApiResponseDto<>(true, SUCCESSFULLY_UPDATED, mapToUserResponseDto(savedUser, userDetails));
    }

    public ApiResponseDto<UserResponseDto> updateUserFields(String email,UserUpdateRequestDto updateData) throws Exception {
        User existingUser = userRepository.findByEmail(email)
                .orElseThrow(() -> new EmailNotFoundException("User with email " + email + " not found."));

        User updatedUser = mapToUserEntity(updateData, existingUser);

        existingUser.setUpdatedAt(LocalDateTime.now());

        User savedUser = userRepository.save(updatedUser);

        return new ApiResponseDto<>(true, "User updated successfully", null);
    }

    private User mapToUserEntity(UserRequestDto user, User existingUser) {
        if (user.getFirstName() != null) existingUser.setFirstName(user.getFirstName());
        if (user.getLastName() != null) existingUser.setLastName(user.getLastName());
        if (user.getCompany() != null) existingUser.setCompany(user.getCompany());
        if (user.getWebsite() != null) existingUser.setWebsite(user.getWebsite());
        if (user.getAddress() != null) existingUser.setAddress(user.getAddress());
        if (user.getPostalCode() != null) existingUser.setPostalCode(user.getPostalCode());
        if (user.getCity() != null) existingUser.setCity(user.getCity());
        if (user.getCountry() != null) existingUser.setCountry(user.getCountry());
        if (user.getCompanyId() != null) existingUser.setCompanyId(user.getCompanyId());
        if (user.getAccountNo() != null) existingUser.setAccountNo(user.getAccountNo());
        if (user.getCurrency() != null) existingUser.setCurrency(user.getCurrency());
        if (user.getMerchantName() != null) existingUser.setMerchantName(user.getMerchantName());
        if (user.getMerchantType() != null) existingUser.setMerchantType(user.getMerchantType());
        if (user.getCPSolutionPartnerId() != null) existingUser.setCPSolutionPartnerId(user.getCPSolutionPartnerId());

        return existingUser;
    }

    private User mapToUserEntity(UserUpdateRequestDto user, User existingUser) {
        if (user.getFirstName() != null) existingUser.setFirstName(user.getFirstName());
        if (user.getLastName() != null) existingUser.setLastName(user.getLastName());
        if(user.getMccCode() != null) existingUser.setMccCode(user.getMccCode());
        if(user.getMerchantType() != null) existingUser.setMerchantType(user.getMerchantType());

        return existingUser;
    }

    private UserDetails mapToUserDetailsEntity(UserDetailsDto userDetailsDto, User user) {
        UserDetails userDetails = new UserDetails();
        userDetails.setUser(user);
        userDetails.setOwnerId(userDetailsDto.getOwnerId());
        userDetails.setSolutionPartnerUid(userDetailsDto.getSolutionPartnerUid());
        userDetails.setUnp(userDetailsDto.getUnp());
        userDetails.setBic(userDetailsDto.getBic());
        userDetails.setIban(userDetailsDto.getIban());
        userDetails.setBankAccountCurrencyId(userDetailsDto.getBankAccountCurrencyId());
        userDetails.setPhone(userDetailsDto.getPhone());
        userDetails.setMerchantStatusId(userDetailsDto.getMerchantStatusId());
        userDetails.setContract(userDetailsDto.getContract());
        userDetails.setContractDate(userDetailsDto.getContractDate());
        userDetails.setPostAddress(userDetailsDto.getPostAddress());
        userDetails.setZip(userDetailsDto.getZip());
        userDetails.setRegisteredAddress(userDetailsDto.getRegisteredAddress());
        userDetails.setIsoMerchantId(userDetailsDto.getIsoMerchantId());
        userDetails.setDefaultLocale(userDetailsDto.getDefaultLocale());
        userDetails.setCustomDataSupport(userDetailsDto.getCustomDataSupport());
        userDetails.setMerchantManagerAccount(userDetailsDto.getMerchantManagerAccount());
        userDetails.setReverseSupport(userDetailsDto.getReverseSupport());
        userDetails.setRefundSupport(userDetailsDto.getRefundSupport());
        userDetails.setNotificationChannel(userDetailsDto.getNotificationChannel());
        userDetails.setNotificationGateUid(userDetailsDto.getNotificationGateUid());
        return userDetails;
    }

    private void assignUserRole(User user, Long roleId) {
        UserRoleId userRoleId = new UserRoleId(user.getId(), roleId);
        UserRole userRole = new UserRole(userRoleId);
        userRoleRepository.save(userRole);
    }

    private UserResponseDto mapToUserResponseDto(User user, UserDetails userDetails) {

        UserResponseDto responseDto = new UserResponseDto();

        responseDto.setId(user.getId());
        responseDto.setFirstName(user.getFirstName());
        responseDto.setLastName(user.getLastName());
        responseDto.setCompany(user.getCompany());
        responseDto.setEmail(user.getEmail());
        responseDto.setWebsite(user.getWebsite());
        responseDto.setAddress(user.getAddress());
        responseDto.setPostalCode(user.getPostalCode());
        responseDto.setCity(user.getCity());
        responseDto.setCountry(user.getCountry());
        responseDto.setCompanyId(user.getCompanyId());
        responseDto.setAccountNo(user.getAccountNo());
        responseDto.setCurrency(user.getCurrency());
        responseDto.setMerchantName(user.getMerchantName());
        responseDto.setParentId(user.getParentId());
        responseDto.setCreatedAt(user.getCreatedAt());
        responseDto.setMerchantType(user.getMerchantType());
        responseDto.setCPSolutionPartnerId(user.getCPSolutionPartnerId());

        if (userDetails != null) {
            responseDto.setUserDetails(new UserDetailsDto(userDetails));
        }

        return responseDto;
    }

    public void enableUser(String email, RegisterPaymentTerminalDTO terminalRequest) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new NoSuchElementException("User not found with email: " + email));

        if (user.getIsEnabled()) {
            throw new IllegalStateException("User with email " + email + " is already enabled");
        }

        if (isTestProfileActive()) {
            logger.debug("Skipping merchant/terminal registration in test environment.");
            user.setIsEnabled(true);
            userRepository.save(user);
            return;
        }

        if (user.getUpdatedAt() == null) {
            throw new UnsupportedOperationException("Please complete user registration before enabling this account");
        }

        try {
            if (user.getMerchantType().equalsIgnoreCase(MerchantType.TERMINAL.name())) {
                enableTerminalUser(user, terminalRequest);
            } else {
                enableMerchantUser(user);
            }

            user.setIsEnabled(true);
            userRepository.save(user);

            emailService.sendEmailWithTemplate(
                    email,
                    BCC,
                    ACCOUNT_ENABLED_NOTIFICATION_SUBJECT,
                    EmailService.ENABLE_USER_TEMPLATE,
                    user.getFirstName(),
                    user.getLastName()
            );
        } catch (Exception e) {
            logger.error("Error enabling user {}: {}", email, e.getMessage(), e);
            throw e; // Re-throw to be handled by the controller
        }
    }

    private void enableTerminalUser(User user, RegisterPaymentTerminalDTO terminalRequest) {
        if (user.getParentId() == null) {
            throw new IllegalStateException("Terminal user must have a parent ID");
        }

        if (terminalRequest == null) {
            throw new IllegalArgumentException("Terminal user requires payment terminal details");
        }

        User parentUser = userRepository.findById(user.getParentId())
                .orElseThrow(() -> new NoSuchElementException("Parent User not found with ID: " + user.getParentId()));

        if (parentUser.getUpdatedAt() == null) {
            throw new UnsupportedOperationException("Please complete user registration of parent account to enable terminal user");
        }

        String newTerminalName = terminalNameGenerator.generateNextTerminalName();
        logger.info("New Terminal Name is: {}", newTerminalName);

        String authToken = fetchAuthToken();
        Integer terminalId = registerPaymentTerminal(terminalRequest, newTerminalName, authToken);

        // Save terminal information
        PaymentTerminal paymentTerminal = new PaymentTerminal();
        paymentTerminal.setParentUser(parentUser);
        paymentTerminal.setTerminalUserId(user.getId());
        paymentTerminal.setTerminalId(String.valueOf(terminalId));
        paymentTerminal.setTerminalName(newTerminalName);
        paymentTerminal.setDeviceId(null);
        paymentTerminalRepository.save(paymentTerminal);
    }

    private void enableMerchantUser(User user) {
        // Get user details
        UserDetails userDetails = userDetailsRepository.findByUserId(user.getId())
                .orElseThrow(() -> new NoSuchElementException("User Details not found for user ID: " + user.getId()));

        MerchantRegistrationRequestDto merchantDto = new MerchantRegistrationRequestDto();
        merchantDto.setOwnerId(userDetails.getOwnerId());
        merchantDto.setSolutionPartnerUid(userDetails.getSolutionPartnerUid());
        merchantDto.setName(user.getFirstName() + " " + user.getLastName());
        merchantDto.setUnp(userDetails.getUnp());
        merchantDto.setBic(userDetails.getBic());
        merchantDto.setIban(userDetails.getIban());
        merchantDto.setBankAccountCurrencyId(userDetails.getBankAccountCurrencyId());
        merchantDto.setEmail(user.getEmail());
        merchantDto.setPhone(userDetails.getPhone());
        merchantDto.setMerchantStatusId(userDetails.getMerchantStatusId());
        merchantDto.setContract(userDetails.getContract());
        merchantDto.setContractDate(userDetails.getContractDate());
        merchantDto.setPostAddress(userDetails.getPostAddress());
        merchantDto.setZip(userDetails.getZip());
        merchantDto.setRegisteredAddress(userDetails.getRegisteredAddress());
        merchantDto.setIsoMerchantId(userDetails.getIsoMerchantId());
        merchantDto.setDefaultLocale(userDetails.getDefaultLocale());
        merchantDto.setCustomDataSupport(userDetails.getCustomDataSupport());
        merchantDto.setMerchantManagerAccount(userDetails.getMerchantManagerAccount());
        merchantDto.setReverseSupport(userDetails.getReverseSupport());
        merchantDto.setRefundSupport(userDetails.getRefundSupport());

        // Register with merchant API
        RestTemplate restTemplate = new RestTemplate();
        String authToken = fetchAuthToken();

        String merchantUrl = ibaBaseUrl + "v1/merchant";
        logger.debug("IBA Base URL: {}", merchantUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(authToken);
        HttpEntity<MerchantRegistrationRequestDto> merchantRequestEntity = new HttpEntity<>(merchantDto, headers);

        ResponseEntity<MerchantRegistrationRequestDto> merchantResponse = restTemplate.postForEntity(
                merchantUrl, merchantRequestEntity, MerchantRegistrationRequestDto.class);

        if (!merchantResponse.getStatusCode().is2xxSuccessful() || merchantResponse.getBody() == null) {
            throw new RuntimeException("Failed to register merchant with IBA API: HTTP Status - " + merchantResponse.getStatusCode());
        }
    }
    public List<User> getChildAccountsByParentId(UUID parentId) {
        return userRepository.findByParentId(parentId);
    }

    public void saveOnboardingRequest(OnboardingRequestDto onboardingRequestDto) throws JsonProcessingException {
        onboardingRequestRepository.findByEmail(onboardingRequestDto.getEmail())
                .ifPresent(existingRequest -> {
                    throw new EmailAlreadyExistsException(
                            "Your onboarding request has already been received. Please wait for processing.");
                });

        OnboardingRequestEntity entity = new OnboardingRequestEntity();
        entity.setEmail(onboardingRequestDto.getEmail());
        entity.setMerchantType(onboardingRequestDto.getMerchantType());
        entity.setCountry(onboardingRequestDto.getCountry());
        entity.setCompanyName(onboardingRequestDto.getCompanyName());
        entity.setPhone(onboardingRequestDto.getPhone());
        entity.setExpectedTransactionsPerMonth(onboardingRequestDto.getExpectedTransactionsPerMonth());
        entity.setExpectedMonthlyActiveTerminals(onboardingRequestDto.getExpectedMonthlyActiveTerminals());

        onboardingRequestRepository.save(entity);

        try {
            createOnboardingRequestTicket(onboardingRequestDto, activeProfile);
        } catch (Exception e) {
            e.printStackTrace();
        }

        emailService.sendOnboardingRequestEmail(
                onboardingRequestDto.getEmail(),
                BCC,
                ONBOARDING_REQUEST_SUBJECT + " - " + onboardingRequestDto.getCompanyName(),
                onboardingRequestDto.getMerchantType(),
                onboardingRequestDto.getCountry(),
                onboardingRequestDto.getCompanyName(),
                onboardingRequestDto.getPhone(),
                onboardingRequestDto.getExpectedTransactionsPerMonth(),
                onboardingRequestDto.getExpectedMonthlyActiveTerminals()
        );
    }


    private String fetchAuthToken() {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, String> payload = new HashMap<>();
        payload.put("username", ibaAPIUsername);
        payload.put("password", ibaAPIPassword);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(payload, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(cardPresentBaseUrl +"card-present/public/authenticate", request, Map.class);

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            return response.getBody().get("data").toString();
        } else {
            throw new RuntimeException("Failed to fetch auth token: " + response.getStatusCode());
        }
    }

    private Integer registerPaymentTerminal(RegisterPaymentTerminalDTO terminalRequest, String terminalName, String authToken) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headers.setBearerAuth(authToken);

        terminalRequest.setName(terminalName);

        HttpEntity<RegisterPaymentTerminalDTO> request = new HttpEntity<>(terminalRequest, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(
                cardPresentBaseUrl + "card-present/public/payment-terminal/register",
                request,
                Map.class
        );

        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            Map<String, Object> responseBody = response.getBody();

            if ((boolean) responseBody.get("status")) {
                Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                if (data != null && data.get("id") != null) {
                    return Integer.parseInt(data.get("id").toString());
                } else {
                    throw new RuntimeException("Response does not contain 'id' in the 'data' object");
                }
            } else {
                throw new RuntimeException("Failed to register payment terminal: " + responseBody.get("message"));
            }
        } else {
            throw new RuntimeException("Failed to register payment terminal: HTTP Status - " + response.getStatusCode());
        }
    }


    public String createIssue(JiraIssueRequestDto issueRequest,String username, String userEmail) throws JsonProcessingException {

        String originalMessage = extractDescriptionText(issueRequest.getFields().getDescription());

        issueRequest.getFields().setSummary("[" + WordUtils.capitalize(activeProfile) + "] " + issueRequest.getFields().getSummary());

        addUserDataToDescription(issueRequest, username, userEmail);

        issueRequest.getFields().setLabels(Collections.singletonList(WordUtils.capitalize(activeProfile)));

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + getEncodedCredentials(jiraUsername, jiraApiToken));
        headers.set("Content-Type", "application/json");

        HttpEntity<JiraIssueRequestDto> request = new HttpEntity<>(issueRequest, headers);
        ResponseEntity<String> response = restTemplate.exchange(jiraUrl + "rest/api/3/issue", HttpMethod.POST, request, String.class);

        JiraIssueResponseDto jiraResponse = objectMapper.readValue(response.getBody(), JiraIssueResponseDto.class);

        emailService.sendSupportTicketEmail(
                userEmail,
                username,
                BCC,
                SUPPORT_TICKET_SUBJECT,
                jiraResponse.getKey(),
                originalMessage
        );

        return jiraResponse.getKey();
    }

    public void createOnboardingRequestTicket(OnboardingRequestDto onboardingRequest, String activeProfile) throws JsonProcessingException {

        JiraIssueRequestDto issueRequest = createOnboardingJiraPayload(onboardingRequest, activeProfile);

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + getEncodedCredentials(jiraUsername, jiraApiToken));
        headers.set("Content-Type", "application/json");

        HttpEntity<JiraIssueRequestDto> request = new HttpEntity<>(issueRequest, headers);
        ResponseEntity<String> response = restTemplate.exchange(jiraUrl + "rest/api/3/issue", HttpMethod.POST, request, String.class);

        JiraIssueResponseDto jiraResponse = objectMapper.readValue(response.getBody(), JiraIssueResponseDto.class);

    }

    private boolean isTestProfileActive() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("test".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }
}

package com.eftaapay.payment.repository;

import com.eftaapay.payment.entity.StaticQR;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface StaticQRRepository extends JpaRepository<StaticQR, UUID> {
    List<StaticQR> findByUserId(UUID userId);
    Optional<StaticQR> findByUserIdAndId(UUID userId, UUID id);

    @Query("SELECT s FROM StaticQR s JOIN FETCH s.user WHERE s.id = :qrcodeId")
    Optional<StaticQR> findByIdWithUser(@Param("qrcodeId") UUID qrcodeId);

    Optional<StaticQR> findById(UUID qrcodeId);
    boolean existsByUserId(UUID userId);
}

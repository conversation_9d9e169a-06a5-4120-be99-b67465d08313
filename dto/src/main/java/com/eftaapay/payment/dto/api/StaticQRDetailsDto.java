package com.eftaapay.payment.dto.api;

import java.math.BigDecimal;

public class StaticQRDetailsDto {

    private String firstName;
    private String lastName;
    private String companyName;
    private String currency;
    private String qrCodeName;
    private BigDecimal defaultAmount;

    public StaticQRDetailsDto() {
    }

    public StaticQRDetailsDto(String firstName, String lastName, String companyName,
                             String currency, String qrCodeName, BigDecimal defaultAmount) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.companyName = companyName;
        this.currency = currency;
        this.qrCodeName = qrCodeName;
        this.defaultAmount = defaultAmount;
    }

    public String getQrCodeName() {
        return qrCodeName;
    }

    public void setQrCodeName(String qrCodeName) {
        this.qrCodeName = qrCodeName;
    }

    public BigDecimal getDefaultAmount() {
        return defaultAmount;
    }

    public void setDefaultAmount(BigDecimal defaultAmount) {
        this.defaultAmount = defaultAmount;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
